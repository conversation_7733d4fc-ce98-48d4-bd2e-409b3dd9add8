import { View, Text, StyleSheet, FlatList, ActivityIndicator, TextInput, TouchableOpacity, Platform, RefreshControl, Alert } from 'react-native';
import React, { useMemo, useState, useRef, useCallback, useEffect } from 'react';
import { Stack, useLocalSearchParams, useFocusEffect } from 'expo-router';
import { useQuery, ApolloError } from '@apollo/client';
import { GET_COMMENTS } from '@/scr/graphql/queries';
import { KeyboardAvoidingView } from 'react-native-keyboard-controller';
import { useCreateComment } from '@/scr/hooks/useCreateComment';
import { useNotification } from '@/scr/context/notificationsContext';


import ThreadCard from '@/scr/components/community/thread/threadCard';
import { useTheme } from '@/scr/context/themeContext';
import { useUser } from '@/scr/context/userContext';
import CommentCard, { Comment } from '@/scr/components/community/comments/CommentCard';
import { ArrowUp, X } from 'lucide-react-native';
import { processContent } from '@/scr/utils/processContent';
import ReactionCountsBottomSheet, { ReactionCountsBottomSheetRef } from '@/scr/components/community/reactions/ReactionCountsBottomSheet';
import { ReactionCount } from '@/scr/graphql/fragments';

interface CommentWithNesting extends Comment {
  nestingLevel: number;
}

const ThreadDetailScreen = () => {
  const { threadId } = useLocalSearchParams<{ threadId: string }>();
  const { theme } = useTheme();
  const { user } = useUser();
  const { isFromNotification, clearNotificationFlag } = useNotification();
  const [commentText, setCommentText] = useState('');
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [replyingTo, setReplyingTo] = useState<{ id: string; displayName: string } | null>(null);
  const textInputRef = useRef<TextInput>(null);
  const [refreshing, setRefreshing] = useState(false);
  const [notificationRefetching, setNotificationRefetching] = useState(false);

  // Shared ReactionCountsBottomSheet state
  const reactionCountsBottomSheetRef = useRef<ReactionCountsBottomSheetRef>(null);
  const [reactionBottomSheetData, setReactionBottomSheetData] = useState<{
    threadId?: string;
    commentId?: string;
    reactionCounts: ReactionCount | null;
  } | null>(null);

  const { data, loading, error, refetch } = useQuery(GET_COMMENTS, {
    variables: {
      threadId,
      limit: 50, // Explicit limit to match cache keys
      offset: 0  // Explicit offset to match cache keys
    },
    skip: !threadId,
    notifyOnNetworkStatusChange: true,
    // Use cache-and-network when coming from notification for fresh data,
    // otherwise use cache-first for optimal performance
    fetchPolicy: isFromNotification ? 'cache-and-network' : 'cache-first',
    // Always use cache-first for subsequent queries
    nextFetchPolicy: 'cache-first',
    // Enable partial data to show cached comments while fetching fresh ones
    returnPartialData: true,
  });

  const { createComment, loading: createCommentLoading } = useCreateComment();

  console.log("User: ", JSON.stringify(user, null, 2));

  // Clear notification flag once the page has loaded and data is available
  useEffect(() => {
    if (isFromNotification && !loading && data) {
      clearNotificationFlag();
    }
  }, [isFromNotification, loading, data, clearNotificationFlag]);

  // Force refetch when page is focused and we came from a notification
  useFocusEffect(
    useCallback(() => {
      if (isFromNotification && threadId && !loading && !notificationRefetching) { 
        console.log('Refetching comments due to notification navigation for thread:', threadId);
        setNotificationRefetching(true);

        // Use a small delay to ensure cache eviction has completed
        const timeoutId = setTimeout(() => {
          refetch()
            .then(() => {
              console.log('Successfully refetched comments from notification');
            })
            .catch((error) => {
              console.error('Failed to refetch comments from notification:', error);
            })
            .finally(() => {
              setNotificationRefetching(false);
            });
        }, 100);

        return () => {
          clearTimeout(timeoutId);
          setNotificationRefetching(false);
        };
      }
    }, [isFromNotification, threadId, refetch, loading, notificationRefetching])
  );

  // Shared handler for reaction counts press
  const handleReactionCountsPress = useCallback((data: {
    threadId?: string;
    commentId?: string;
    reactionCounts: ReactionCount | null;
  }) => {
    setReactionBottomSheetData(data);
    reactionCountsBottomSheetRef.current?.present();
  }, []);

  const isNetworkError = (error: ApolloError): boolean => {
    return error.networkError !== null ||
      error.message?.includes('Network error') ||
      error.message?.includes('Failed to fetch') ||
      error.message?.includes('network') ||
      error.graphQLErrors?.some(err => err.extensions?.code === 'NETWORK_ERROR') ||
      false;
  };

  const handleRefresh = useCallback(async () => {
    if (refreshing) return;

    setRefreshing(true);
    try {
      await refetch();
    } catch (error) {
      console.error('Error refreshing comments:', error);
      if (error instanceof Error && isNetworkError(error as ApolloError)) {
        Alert.alert(
          'Connection Problem',
          'Unable to refresh comments. Please check your internet connection and try again.',
          [{ text: 'OK' }]
        );
      }
    } finally {
      setRefreshing(false);
    }
  }, [refetch, refreshing]);

  const handleStartReply = useCallback((comment: Comment) => {
    setReplyingTo({ id: comment._id, displayName: comment.author.displayName });
    textInputRef.current?.focus();
  }, []);

  const handleCancelReply = useCallback(() => {
    setReplyingTo(null);
  }, []);

  const handleSubmitComment = async () => {
    if (!commentText.trim() || isSubmitting || createCommentLoading || !user || !threadId) return;

    console.log('Submitting comment:', {
      threadId,
      content: processContent(commentText),
      parentCommentId: replyingTo?.id,
      user: user.displayName
    });

    setIsSubmitting(true);

    try {
      const result = await createComment({
        threadId,
        content: processContent(commentText),
        parentCommentId: replyingTo?.id,
      });

      console.log('Comment creation result:', result);

      // Clear the input and reset submitting state on success
      setCommentText('');
      setReplyingTo(null);
      setIsSubmitting(false);
    } catch (error) {
      console.error('Failed to submit comment:', error);
      setIsSubmitting(false);
    }
  };

  // Organize comments hierarchically
  const organizedComments = useMemo(() => {
    if (!data?.comments || data.comments.length === 0) return [];

    const comments: Comment[] = data.comments;
    const childrenByParentId = new Map<string, Comment[]>();
    const rootComments: Comment[] = [];

    // Separate root comments and group children by parent ID in a single pass
    comments.forEach(comment => {
      if (comment.parentCommentId) {
        if (!childrenByParentId.has(comment.parentCommentId)) {
          childrenByParentId.set(comment.parentCommentId, []);
        }
        childrenByParentId.get(comment.parentCommentId)!.push(comment);
      } else {
        rootComments.push(comment);
      }
    });

    // Recursive function to flatten the comment tree with nesting levels
    const flattenComments = (commentsToProcess: Comment[], nestingLevel: number): CommentWithNesting[] => {
      const result: CommentWithNesting[] = [];
      commentsToProcess.forEach(comment => {
        result.push({ ...comment, nestingLevel });
        const children = childrenByParentId.get(comment._id);
        if (children) {
          // Sort children by creation date if needed, assuming they come sorted from the backend for now
          result.push(...flattenComments(children, nestingLevel + 1));
        }
      });
      return result;
    };

    return flattenComments(rootComments, 0);
  }, [data?.comments]);

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flex: 1,
      backgroundColor: theme.colors.Background.background0,
    },
    listContainer: {
      flex: 1,
      paddingHorizontal: theme.spacing.spacing.s4,
    },
    centered: {
      flex: 1,
      justifyContent: 'center',
      alignItems: 'center',
    },
    separator: {
      height: 1,
      backgroundColor: theme.colors.Background.background100,
      marginVertical: theme.spacing.spacing.s4,
    },
    commentInputContainer: {
      paddingHorizontal: theme.spacing.spacing.s4,
      paddingVertical: theme.spacing.spacing.s3,
      backgroundColor: theme.colors.Background.background0,
    },
    replyingToContainer: {
      flexDirection: 'row',
      justifyContent: 'space-between',
      alignItems: 'center',
      paddingBottom: theme.spacing.spacing.s2,
      paddingHorizontal: theme.spacing.spacing.s1,
    },
    replyingToText: {
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text500,
    },
    replyingToName: {
      ...theme.textVariants.text('sm', 'semibold'),
      color: theme.colors.Text.text700,
    },
    commentInput: {
      flex: 1,
      ...theme.textVariants.text('sm', 'regular'),
      color: theme.colors.Text.text950,
      backgroundColor: theme.colors.Background.background50,
      borderRadius: theme.spacing.borderRadius.xl3,
      paddingHorizontal: theme.spacing.spacing.s3,
      paddingTop: 10,
      paddingBottom: 10,
      marginRight: theme.spacing.spacing.s2,
      maxHeight: 100,
      minHeight: 40,
      borderWidth: 1,
      borderColor: theme.colors.Background.background200,
    },
    sendButton: {
      width: 40,
      height: 40,
      borderRadius: 20,
      backgroundColor: theme.colors.Primary.primary500,
      justifyContent: 'center',
      alignItems: 'center',
    },
    sendButtonDisabled: {
      backgroundColor: theme.colors.Background.background300,
    },
  }), [theme]);

  if (!threadId) {
    return (
      <View style={styles.centered}>
        <Text>Thread ID is missing.</Text>
      </View>
    );
  }

  const memoizedHeader = useMemo(() => (
    <View>
      <ThreadCard
        threadId={threadId}
        isClickable={false}
        onReactionCountsPress={handleReactionCountsPress}
      />
      <View style={styles.separator} />
    </View>
  ), [threadId, styles, handleReactionCountsPress]);

  const canSubmit = commentText.trim().length > 0 && !isSubmitting && !createCommentLoading && user;

  const renderComment = useCallback(({ item }: { item: CommentWithNesting }) => (
    <CommentCard
      comment={item}
      nestingLevel={item.nestingLevel}
      onReply={handleStartReply}
      onReactionCountsPress={handleReactionCountsPress}
    />
  ), [handleStartReply, handleReactionCountsPress]);

  return (
    <KeyboardAvoidingView
      style={styles.container}
      behavior={Platform.OS === 'ios' ? 'padding' : 'height'}
      keyboardVerticalOffset={Platform.OS === 'ios' ? 0 : 0}
    >
      <Stack.Screen options={{ title: 'Post' }} />
      <FlatList
        style={styles.listContainer}
        data={organizedComments}
        keyExtractor={(item) => item._id}
        ListHeaderComponent={memoizedHeader}
        renderItem={renderComment}
        showsVerticalScrollIndicator={false}
        refreshControl={
          <RefreshControl
            refreshing={refreshing}
            onRefresh={handleRefresh}
            tintColor={theme.colors.Background.background500}
            colors={[theme.colors.Background.background500]}
          />
        }
        ListFooterComponent={() => {
          if (loading) return <ActivityIndicator style={{ marginVertical: 20 }} />;
          if (error) return <Text style={{ color: theme.colors.Text.text0, textAlign: 'center', marginVertical: 20 }}>Error loading comments.</Text>;
          if (organizedComments.length === 0 && !loading) {
            return (
              <View style={{ marginTop: 20, alignItems: 'center', paddingBottom: 20 }}>
                <Text style={{ ...theme.textVariants.text('lg', 'regular'), color: theme.colors.Text.text500, textAlign: 'center' }}>
                  No comments yet.
                </Text>
                <Text style={{ ...theme.textVariants.text('md', 'regular'), color: theme.colors.Text.text500, textAlign: 'center', marginTop: theme.spacing.spacing.s1 }}>
                  Be the first to share your thoughts!
                </Text>
              </View>
            );
          }
          return <View style={{ height: 20 }} />; // Add some bottom spacing
        }}
      />

      {/* Comment Input */}
      <View style={styles.commentInputContainer}>
        <View>
          {replyingTo && (
            <View style={styles.replyingToContainer}>
              <Text style={styles.replyingToText}>
                Replying to <Text style={styles.replyingToName}>@{replyingTo.displayName}</Text>
              </Text>
              <TouchableOpacity onPress={handleCancelReply}>
                <X size={16} color={theme.colors.Text.text500} />
              </TouchableOpacity>
            </View>
          )}
          <View style={{ flexDirection: 'row', alignItems: 'flex-end' }}>
            <TextInput
              ref={textInputRef}
              style={styles.commentInput}
              placeholder={replyingTo ? "Add a reply..." : "Add a comment..."}
              placeholderTextColor={theme.colors.Text.text500}
              value={commentText}
              onChangeText={setCommentText}
              multiline
              maxLength={1000}
            />
            <TouchableOpacity
              style={[
                styles.sendButton,
                !canSubmit && styles.sendButtonDisabled
              ]}
              onPress={handleSubmitComment}
              disabled={!canSubmit}
            >
              {(isSubmitting || createCommentLoading) ? (
                <ActivityIndicator size="small" color={theme.colors.Background.background0} />
              ) : (
                <ArrowUp
                  size={24}
                  color={canSubmit ? theme.colors.Background.background0 : theme.colors.Text.text500}
                />
              )}
            </TouchableOpacity>
          </View>
        </View>
      </View>

      {/* Shared ReactionCountsBottomSheet */}
      <ReactionCountsBottomSheet
        ref={reactionCountsBottomSheetRef}
        reactionCounts={reactionBottomSheetData?.reactionCounts || null}
        threadId={reactionBottomSheetData?.threadId}
        commentId={reactionBottomSheetData?.commentId}
      />
    </KeyboardAvoidingView>
  );
};

export default ThreadDetailScreen;

import { Apollo<PERSON>lient } from '@apollo/client';
import { GET_THREAD_REACTIONS, GET_COMMENT_REACTIONS } from '../graphql/queries';

export const invalidateReactionCache = (
  client: ApolloClient<any>,
  type: 'thread' | 'comment',
  id: string
) => {
  const query = type === 'thread' ? GET_THREAD_REACTIONS : GET_COMMENT_REACTIONS;
  const variables = type === 'thread' ? { threadId: id } : { commentId: id };

  // Remove the query from cache to force refetch next time
  client.cache.evict({
    fieldName: type === 'thread' ? 'threadReactions' : 'commentReactions',
    args: variables,
  });
  
  // Clean up any dangling references
  client.cache.gc();
};

export const updateReactionCacheAfterMutation = (
  client: ApolloClient<any>,
  type: 'thread' | 'comment',
  id: string
) => {
  // Invalidate the reaction details cache when reactions change
  invalidateReactionCache(client, type, id);
  
  // We could also optimistically update the cache here if we had the new data
  // But since reaction details are complex (user info), it's safer to invalidate and refetch
};
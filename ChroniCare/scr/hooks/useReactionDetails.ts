import { useQuery, useLazyQuery, useApolloClient } from '@apollo/client';
import { GET_THREAD_REACTIONS, GET_COMMENT_REACTIONS } from '../graphql/queries';
import { useCallback, useMemo, useEffect } from 'react';

export interface ReactorData {
  id: string;
  displayName: string;
  condition?: string;
  userType: string;
  photoURL?: string;
  reactionType: string;
}

interface ThreadReactionsResponse {
  threadReactions: ReactorData[];
}

interface CommentReactionsResponse {
  commentReactions: ReactorData[];
}

export const useThreadReactionDetails = (threadId: string) => {
  const client = useApolloClient();
  
  // Check if we already have cached data
  const cachedData = useMemo(() => {
    if (!threadId || threadId === '') return null;
    try {
      const cached = client.readQuery<ThreadReactionsResponse>({
        query: GET_THREAD_REACTIONS,
        variables: { threadId },
      });
      return cached?.threadReactions || null;
    } catch {
      return null;
    }
  }, [client, threadId]);

  const [getThreadReactions, { data, loading, error, called }] = useLazyQuery<ThreadReactionsResponse>(
    GET_THREAD_REACTIONS,
    {
      fetchPolicy: 'cache-first', // Try cache first, then network
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    }
  );

  const fetchReactions = useCallback((forceFetch = false) => {
    if (threadId && threadId !== '') {
      console.log(`[ThreadReactions] fetchReactions called for ${threadId}, forceFetch: ${forceFetch}, cachedData: ${!!cachedData}`);
      if (forceFetch || !cachedData) {
        getThreadReactions({
          variables: { threadId },
          fetchPolicy: forceFetch ? 'cache-and-network' : 'cache-first'
        });
      }
    }
  }, [threadId, cachedData, getThreadReactions]);

  const refetchReactions = useCallback(() => {
    if (threadId && threadId !== '') {
      getThreadReactions({
        variables: { threadId },
        fetchPolicy: 'network-only', // Force fresh data
      });
    }
  }, [threadId, getThreadReactions]);

  return {
    fetchReactions,
    refetchReactions,
    reactions: data?.threadReactions || cachedData || [],
    loading: loading && !cachedData,
    error,
    hasData: !!(data?.threadReactions || cachedData),
    fromCache: !!cachedData && !called,
  };
};

export const useCommentReactionDetails = (commentId: string) => {
  const client = useApolloClient();
  
  // Check if we already have cached data
  const cachedData = useMemo(() => {
    if (!commentId || commentId === '') return null;
    try {
      const cached = client.readQuery<CommentReactionsResponse>({
        query: GET_COMMENT_REACTIONS,
        variables: { commentId },
      });
      return cached?.commentReactions || null;
    } catch {
      return null;
    }
  }, [client, commentId]);

  const [getCommentReactions, { data, loading, error, called }] = useLazyQuery<CommentReactionsResponse>(
    GET_COMMENT_REACTIONS,
    {
      fetchPolicy: 'cache-first', // Try cache first, then network
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    }
  );

  const fetchReactions = useCallback((forceFetch = false) => {
    if (commentId && commentId !== '') {
      console.log(`[CommentReactions] fetchReactions called for ${commentId}, forceFetch: ${forceFetch}, cachedData: ${!!cachedData}`);
      if (forceFetch || !cachedData) {
        getCommentReactions({
          variables: { commentId },
          fetchPolicy: forceFetch ? 'cache-and-network' : 'cache-first'
        });
      }
    }
  }, [commentId, cachedData, getCommentReactions]);

  const refetchReactions = useCallback(() => {
    if (commentId && commentId !== '') {
      getCommentReactions({
        variables: { commentId },
        fetchPolicy: 'network-only', // Force fresh data
      });
    }
  }, [commentId, getCommentReactions]);

  return {
    fetchReactions,
    refetchReactions,
    reactions: data?.commentReactions || cachedData || [],
    loading: loading && !cachedData,
    error,
    hasData: !!(data?.commentReactions || cachedData),
    fromCache: !!cachedData && !called,
  };
};
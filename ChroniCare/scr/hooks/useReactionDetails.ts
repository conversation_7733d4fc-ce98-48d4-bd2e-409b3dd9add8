import { useQuery, useLazyQuery, useApolloClient } from '@apollo/client';
import { GET_THREAD_REACTIONS, GET_COMMENT_REACTIONS } from '../graphql/queries';
import { useCallback, useMemo } from 'react';

export interface ReactorData {
  id: string;
  displayName: string;
  condition?: string;
  userType: string;
  photoURL?: string;
  reactionType: string;
}

interface ThreadReactionsResponse {
  threadReactions: ReactorData[];
}

interface CommentReactionsResponse {
  commentReactions: ReactorData[];
}

export const useThreadReactionDetails = (threadId: string) => {
  const client = useApolloClient();
  
  // Check if we already have cached data
  const cacheKey = `threadReactions:${threadId}`;
  const cachedData = useMemo(() => {
    try {
      const cached = client.readQuery<ThreadReactionsResponse>({
        query: GET_THREAD_REACTIONS,
        variables: { threadId },
      });
      return cached?.threadReactions || null;
    } catch {
      return null;
    }
  }, [client, threadId]);

  const [getThreadReactions, { data, loading, error, called }] = useLazyQuery<ThreadReactionsResponse>(
    GET_THREAD_REACTIONS,
    {
      variables: { threadId },
      fetchPolicy: 'cache-first', // Try cache first, then network
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    }
  );

  const fetchReactions = useCallback(() => {
    if (threadId && !cachedData) {
      getThreadReactions();
    }
  }, [threadId, cachedData, getThreadReactions]);

  const refetchReactions = useCallback(() => {
    if (threadId) {
      getThreadReactions({
        fetchPolicy: 'network-only', // Force fresh data
      });
    }
  }, [threadId, getThreadReactions]);

  return {
    fetchReactions,
    refetchReactions,
    reactions: data?.threadReactions || cachedData || [],
    loading: loading && !cachedData,
    error,
    hasData: !!(data?.threadReactions || cachedData),
    fromCache: !!cachedData && !called,
  };
};

export const useCommentReactionDetails = (commentId: string) => {
  const client = useApolloClient();
  
  // Check if we already have cached data
  const cachedData = useMemo(() => {
    try {
      const cached = client.readQuery<CommentReactionsResponse>({
        query: GET_COMMENT_REACTIONS,
        variables: { commentId },
      });
      return cached?.commentReactions || null;
    } catch {
      return null;
    }
  }, [client, commentId]);

  const [getCommentReactions, { data, loading, error, called }] = useLazyQuery<CommentReactionsResponse>(
    GET_COMMENT_REACTIONS,
    {
      variables: { commentId },
      fetchPolicy: 'cache-first', // Try cache first, then network
      errorPolicy: 'all',
      notifyOnNetworkStatusChange: true,
    }
  );

  const fetchReactions = useCallback(() => {
    if (commentId && !cachedData) {
      getCommentReactions();
    }
  }, [commentId, cachedData, getCommentReactions]);

  const refetchReactions = useCallback(() => {
    if (commentId) {
      getCommentReactions({
        fetchPolicy: 'network-only', // Force fresh data
      });
    }
  }, [commentId, getCommentReactions]);

  return {
    fetchReactions,
    refetchReactions,
    reactions: data?.commentReactions || cachedData || [],
    loading: loading && !cachedData,
    error,
    hasData: !!(data?.commentReactions || cachedData),
    fromCache: !!cachedData && !called,
  };
};
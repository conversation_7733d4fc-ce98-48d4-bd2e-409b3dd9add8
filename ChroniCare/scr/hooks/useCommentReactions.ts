import { useMutation, useApolloClient, gql } from '@apollo/client';
import { ReactionCount } from '../graphql/fragments';
import { useState, useCallback, useRef, useEffect } from 'react';
import { updateReactionCacheAfterMutation } from '../utils/reactionCacheUtils';

const ADD_COMMENT_REACTION = gql`
  mutation AddCommentReaction($commentId: ID!, $reaction: String!) {
    addCommentReaction(commentId: $commentId, reaction: $reaction) {
      _id
      reactionCounts {
        love
        withYou
        funny
        insightful
        poop
      }
      myReaction
    }
  }
`;

type ReactionType = keyof ReactionCount;

interface Comment {
  _id: string;
  reactionCounts: ReactionCount;
  myReaction: string | null;
}

interface AddCommentReactionResult {
  addCommentReaction: {
    _id: string;
    reactionCounts: ReactionCount;
    myReaction: string | null;
  };
}

export const useCommentReactions = (commentId: string) => {
  const client = useApolloClient();
  const [errorMessage, setErrorMessage] = useState<string | null>(null);
  const retryTimeoutRef = useRef<ReturnType<typeof setTimeout> | null>(null);

  // Optimized cache reading using readFragment instead of full query
  const getCurrentComment = useCallback((): Comment | null => {
    try {
      return client.readFragment({
        id: client.cache.identify({ __typename: 'Comment', _id: commentId }),
        fragment: gql`
          fragment CommentReactionFragment on Comment {
            _id
            reactionCounts {
              love
              withYou
              funny
              insightful
              poop
            }
            myReaction
          }
        `,
      }) as Comment | null;
    } catch {
      return null;
    }
  }, [client, commentId]);

  // Retry mechanism with exponential backoff
  const retryReaction = useCallback((reaction: ReactionType, retryCount = 0) => {
    if (retryCount >= 3) {
      setErrorMessage('Unable to add reaction. Please check your connection and try again.');
      return;
    }

    if (retryTimeoutRef.current) {
      clearTimeout(retryTimeoutRef.current);
    }

    retryTimeoutRef.current = setTimeout(() => {
      addReactionMutation({
        variables: { commentId, reaction },
      }).catch(() => {
        retryReaction(reaction, retryCount + 1);
      });
    }, Math.pow(2, retryCount) * 1000); // Exponential backoff: 1s, 2s, 4s
  }, [commentId]);

  const [addReactionMutation, { loading, error }] = useMutation<AddCommentReactionResult>(ADD_COMMENT_REACTION, {
    optimisticResponse: (variables, { IGNORE }) => {
      const comment = getCurrentComment();

      // Skip optimistic update if comment not in cache - let Apollo handle normally
      if (!comment) {
        return IGNORE;
      }

      const newReaction = variables.reaction as ReactionType;
      const oldReaction = comment.myReaction as ReactionType | null;
      const newCounts = { ...comment.reactionCounts };

      if (oldReaction) {
        // Decrement the old reaction count
        newCounts[oldReaction] = Math.max(0, (newCounts[oldReaction] || 1) - 1);
      }

      // If the new reaction is different from the old one, increment it
      if (oldReaction !== newReaction) {
        newCounts[newReaction] = (newCounts[newReaction] || 0) + 1;
      }

      return {
        addCommentReaction: {
          _id: variables.commentId,
          reactionCounts: newCounts,
          myReaction: oldReaction === newReaction ? null : newReaction,
        },
      };
    },
    update: (cache, { data }) => {
      if (!data?.addCommentReaction) return;

      // Update all relevant queries in cache
      cache.modify({
        id: cache.identify({ __typename: 'Comment', _id: commentId }),
        fields: {
          reactionCounts: () => data.addCommentReaction.reactionCounts,
          myReaction: () => data.addCommentReaction.myReaction,
        },
      });
    },
    onError: (apolloError) => {
      console.error('Failed to add comment reaction:', apolloError);

      // Set user-friendly error message
      if (apolloError.networkError) {
        setErrorMessage('Network error. Please check your connection.');
      } else if (apolloError.graphQLErrors?.length > 0) {
        setErrorMessage('Unable to add reaction. Please try again.');
      } else {
        setErrorMessage('Something went wrong. Please try again.');
      }
    },
    onCompleted: () => {
      // Clear error message on successful completion
      setErrorMessage(null);
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
        retryTimeoutRef.current = null;
      }
      
      // Invalidate reaction details cache to ensure fresh data next time
      updateReactionCacheAfterMutation(client, 'comment', commentId);
    },
  });

  // Non-debounced reaction function for immediate optimistic updates
  const addReaction = useCallback(async (reaction: ReactionType) => {
    if (loading) return; // Prevent concurrent requests

    // Clear any previous error messages
    setErrorMessage(null);

    try {
      await addReactionMutation({
        variables: { commentId, reaction },
      });
    } catch (err) {
      // Error is handled by onError callback
      console.error('Comment reaction failed:', err);
    }
  }, [addReactionMutation, loading, commentId, setErrorMessage]);

  // Cleanup timeout on unmount
  useEffect(() => {
    return () => {
      if (retryTimeoutRef.current) {
        clearTimeout(retryTimeoutRef.current);
      }
    };
  }, []);

  return {
    addReaction,
    loading,
    error,
    errorMessage,
    getCurrentComment,
  };
};

import React, { useMemo } from 'react';
import { View, Text, StyleSheet, Image } from 'react-native';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import AvatarPlaceholder from '@/scr/components/global/AvatarPlaceholder';
import ReactionIcon from './ReactionIcon';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

interface ReactorCardProps {
  displayName: string;
  condition?: string;
  userType: string;
  photoURL?: string;
  reactionType: ReactionType;
}

const ReactorCard: React.FC<ReactorCardProps> = ({
  displayName,
  condition,
  userType,
  photoURL,
  reactionType,
}) => {
  const { theme } = useTheme();

  const styles = useMemo(() => StyleSheet.create({
    container: {
      flexDirection: 'row',
      alignItems: 'center',
      paddingVertical: theme.spacing.spacing.s3,
    },
    imageContainer: {
      position: 'relative',
      marginRight: theme.spacing.spacing.s3,
    },
    profileImage: {
      width: 32,
      height: 32,
      borderRadius: 999,
    },
    reactionIconContainer: {
      position: 'absolute',
      bottom: -4,
      right: -4,
      backgroundColor: theme.colors.Background.background0,
      borderRadius: 999,
      padding: 2,
    },
    userInfo: {
      flex: 1,
    },
    displayName: {
      ...theme.textVariants.text('md', 'regular'),
      color: theme.colors.Text.text900,
      marginBottom: theme.spacing.spacing.s0_5, 
    },
    userDetails: {
      flexDirection: 'row',
      alignItems: 'center',
    },
    userType: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text700,
    },
    dot: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text700,
      marginHorizontal: theme.spacing.spacing.s1,
    },
    condition: {
      ...theme.textVariants.text('xs', 'regular'),
      color: theme.colors.Text.text700,
    },
  }), [theme]);

  return (
    <View style={styles.container}>
      <View style={styles.imageContainer}>
        {photoURL ? (
          <Image source={{ uri: photoURL }} style={styles.profileImage} />
        ) : (
          <AvatarPlaceholder
            name={displayName}
            size={32}
          />
        )}
        <View style={styles.reactionIconContainer}>
          <ReactionIcon
            reactionType={reactionType}
            isSelected={true}
            size={8}
            showBorder={false}
            showBackground={true}
          />
        </View>
      </View>
      
      <View style={styles.userInfo}>
        <Text style={styles.displayName}>{displayName}</Text>
        <View style={styles.userDetails}>
          <Text style={styles.userType}>{userType}</Text>
          {condition && (
            <>
              <Text style={styles.dot}>•</Text>
              <Text style={styles.condition}>{condition}</Text>
            </>
          )}
        </View>
      </View>
    </View>
  );
};

export default ReactorCard;

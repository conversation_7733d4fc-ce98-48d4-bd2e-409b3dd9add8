import React, { useState, useCallback, useMemo, forwardRef, useImperativeHandle } from 'react';
import { View, Text, StyleSheet, TouchableOpacity, ScrollView } from 'react-native';
import {
  BottomSheetModal,
  BottomSheetBackdrop,
  BottomSheetBackdropProps,
  BottomSheetFlashList,
} from '@gorhom/bottom-sheet';
import { useTheme } from '@/scr/context/themeContext';
import { ReactionCount } from '@/scr/graphql/fragments';
import ReactorCard from './reactorCard';
import ReactionIcon from './ReactionIcon';
import { useThreadReactionDetails, useCommentReactionDetails, ReactorData } from '@/scr/hooks/useReactionDetails';

type ReactionType = keyof Omit<ReactionCount, '__typename'>;

interface ReactionCountsBottomSheetProps {
  reactionCounts: ReactionCount | null;
  threadId?: string;
  commentId?: string;
}

export interface ReactionCountsBottomSheetRef {
  present: () => void;
  dismiss: () => void;
  refreshData: () => void;
}

const ReactionCountsBottomSheet = forwardRef<ReactionCountsBottomSheetRef, ReactionCountsBottomSheetProps>(
  ({ reactionCounts, threadId, commentId }, ref) => {
    const { theme } = useTheme();
    const bottomSheetModalRef = React.useRef<BottomSheetModal>(null);
    const [selectedTab, setSelectedTab] = useState<ReactionType | 'all'>('all');

    // Only call hooks when we have valid IDs to prevent cache issues
    const threadReactionHook = useThreadReactionDetails(threadId || '');
    const commentReactionHook = useCommentReactionDetails(commentId || '');

    // Determine which hook to use based on props
    const reactionHook = threadId ? threadReactionHook : commentReactionHook;
    const { fetchReactions, refetchReactions, reactions } = reactionHook;

    // Snap points for the bottom sheet
    const snapPoints = useMemo(() => ['50%', '75%'], []);

    // Expose methods to parent component
    useImperativeHandle(ref, () => ({
      present: () => {
        setSelectedTab('all'); // Reset to 'All' tab when opening
        // Force fetch when presenting to ensure we have the latest data
        fetchReactions(true);
        bottomSheetModalRef.current?.present();
      },
      dismiss: () => {
        bottomSheetModalRef.current?.dismiss();
      },
      refreshData: () => {
        refetchReactions(); // Force fresh data from network
      },
    }));

    // Backdrop component
    const renderBackdrop = useCallback(
      (props: BottomSheetBackdropProps) => (
        <BottomSheetBackdrop
          {...props}
          disappearsOnIndex={-1}
          appearsOnIndex={1}
          opacity={0.5}
        />
      ),
      []
    );

    const styles = useMemo(
      () =>
        StyleSheet.create({
          container: {
            // This is no longer used as the main wrapper
            padding: theme.spacing.spacing.s4,
          },
          
          tabContainer: {
            borderBottomWidth: 1,
            borderBottomColor: theme.colors.Background.background100,
            marginBottom: theme.spacing.spacing.s4,
            paddingHorizontal: theme.spacing.spacing.s4,
          },
          listContainer: {
            flex: 1,
          },
          tabScrollView: {
            maxHeight: 48,
          },
          tabsRow: {
            flexDirection: 'row',
            gap: theme.spacing.spacing.s2,
          },
          tab: {
            paddingVertical: theme.spacing.spacing.s2,
            flexDirection: 'row',
            alignItems: 'center',
            gap: theme.spacing.spacing.s1_5,
            position: 'relative',
            borderBottomWidth: 2,
            borderBottomColor: 'transparent',
          },
          tabActive: {
            borderBottomColor: theme.colors.Primary.primary500,
          },
          tabText: {
            ...theme.textVariants.text('sm', 'semibold'),
            color: theme.colors.Text.text600,
          },
          tabTextActive: {
            color: theme.colors.Primary.primary500,
          },
          tabCount: {
            ...theme.textVariants.text('sm', 'regular'),
            color: theme.colors.Text.text900,
          },
          tabCountActive: {
            color: theme.colors.Primary.primary500,
          },
        }),
      [theme]
    );

    // Calculate total reactions
    const totalReactions = useMemo(() => {
      if (!reactionCounts) return 0;
      return (
        (reactionCounts.love || 0) +
        (reactionCounts.withYou || 0) +
        (reactionCounts.funny || 0) +
        (reactionCounts.insightful || 0) +
        (reactionCounts.poop || 0)
      );
    }, [reactionCounts]);

    // Get available reaction types with counts
    const availableReactions = useMemo(() => {
      const counts: Record<ReactionType, number> = {
        love: 0,
        withYou: 0,
        funny: 0,
        insightful: 0,
        poop: 0,
      };

      reactions.forEach(reactor => {
        const reactionType = reactor.reactionType as ReactionType;
        if (reactionType in counts) {
          counts[reactionType]++;
        }
      });

      return Object.entries(counts)
        .filter(([_, count]) => count > 0)
        .map(([reaction, count]) => ({ reaction: reaction as ReactionType, count }));
    }, [reactions]);

    // Filter data based on selected tab
    const filteredData = useMemo(() => {
      if (selectedTab === 'all') {
        return reactions;
      }
      return reactions.filter(reactor => reactor.reactionType === selectedTab);
    }, [reactions, selectedTab]);

    // Tab press handlers
    const handleTabPress = useCallback((tab: ReactionType | 'all') => {
      setSelectedTab(tab);
    }, []);

    const renderReactor = useCallback(({ item }: { item: ReactorData }) => (
      <ReactorCard
        displayName={item.displayName}
        condition={item.condition}
        userType={item.userType}
        photoURL={item.photoURL}
        reactionType={item.reactionType as ReactionType}
      />
    ), []);

    const ListHeader = () => (
      <View
        style={styles.tabContainer}
        onLayout={(event) => console.log('Tab Container Layout:', event.nativeEvent.layout)}
      >
        <ScrollView horizontal showsHorizontalScrollIndicator={false}>
          <View style={styles.tabsRow}>
            {/* All Tab */}
            <TouchableOpacity
              style={[styles.tab, selectedTab === 'all' && styles.tabActive]}
              onPress={() => handleTabPress('all')}
            >
              <Text style={[styles.tabText, selectedTab === 'all' && styles.tabTextActive]}>
                All
              </Text>
              <Text style={[styles.tabCount, selectedTab === 'all' && styles.tabCountActive]}>
                {reactions.length}
              </Text>
            </TouchableOpacity>

            {/* Reaction Tabs */}
            {availableReactions.map(({ reaction, count }) => {
              const isActive = selectedTab === reaction;

              return (
                <TouchableOpacity
                  key={reaction}
                  style={[styles.tab, isActive && styles.tabActive]}
                  onPress={() => handleTabPress(reaction)}
                >
                  <ReactionIcon
                    reactionType={reaction}
                    isSelected={true}
                    size={24}
                    showBorder={false}
                    showBackground={false}
                  />
                  <Text style={[styles.tabCount, isActive && styles.tabCountActive]}>
                    {count}
                  </Text>
                </TouchableOpacity>
              );
            })}
          </View>
        </ScrollView>
      </View>
    );

    return (
      <BottomSheetModal
        ref={bottomSheetModalRef}
        index={0}
        snapPoints={snapPoints}
        backdropComponent={renderBackdrop}
        backgroundStyle={{
          backgroundColor: theme.colors.Background.background0,
        }}
        handleIndicatorStyle={{
          backgroundColor: theme.colors.Background.background900,
        }}
        enableOverDrag={false}
        enableDynamicSizing={false}
        enableContentPanningGesture={false}
      >
        <ListHeader/>
        <BottomSheetFlashList
          onLayout={(event) => console.log('FlashList Layout:', event.nativeEvent.layout)}
          data={filteredData}
          renderItem={renderReactor}
          keyExtractor={(item) => item.id}
          estimatedItemSize={100}
          showsVerticalScrollIndicator={false}
          bounces={true}
          scrollEnabled={true}
          contentContainerStyle={{
            paddingHorizontal: theme.spacing.spacing.s4,
            paddingBottom: theme.spacing.spacing.s4,
          }}
        />
      </BottomSheetModal>
    );
  }
);

ReactionCountsBottomSheet.displayName = 'ReactionCountsBottomSheet';

export default ReactionCountsBottomSheet;

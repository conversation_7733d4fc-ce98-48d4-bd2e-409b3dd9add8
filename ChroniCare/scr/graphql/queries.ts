import { gql } from '@apollo/client';
import { THREAD_FRAGMENT } from './fragments';


// The magic "me" query that upserts the user
export const GET_ME = gql`
  query GetMe {
    me {
      id
      firebaseUid
      email
      displayName
      firstName
      lastName
      photoURL
      onboardingCompleted
      updatedAt
      condition
      userType
      communities {
        id
        name
        displayName
      }
    }
  }
`;

export const CREATE_USER = gql`
  mutation CreateUser($input: CreateUserInput!) {
    createUser(input: $input) {
      id
      firebaseUid
      email
      displayName
      photoURL
      onboardingCompleted
 
    }
  }
`;

export const COMPLETE_ONBOARDING = gql`
  mutation CompleteOnboarding($data: CompleteOnboardingInput!) {
    completeOnboarding(data: $data)
  }
`;

export const UPDATE_PROFILE_PICTURE = gql`
  mutation UpdateProfilePicture($photoURL: String!) {
    updateProfilePicture(photoURL: $photoURL) {
      id
      photoURL
      updatedAt
    }
  }
`;

// Storage mutations for Google Cloud Storage profile image upload
export const CREATE_PROFILE_IMAGE_UPLOAD_URL = gql`
  mutation CreateProfileImageUploadUrl($contentType: String!) {
    createProfileImageUploadUrl(contentType: $contentType) {
      signedUrl
      publicUrl
    }
  }
`;

export const UPDATE_USER_PROFILE_IMAGE = gql`
  mutation UpdateUserProfileImage($profileImageUrl: String!) {
    updateUserProfileImage(profileImageUrl: $profileImageUrl) {
      id
      photoURL
      updatedAt
    }
  }
`;

export const CREATE_THREAD_IMAGE_UPLOAD_URL = gql`
  mutation CreateThreadImageUploadUrl($contentType: String!) {
    createThreadImageUploadUrl(contentType: $contentType) {
      signedUrl
      publicUrl
    }
  }
`;

export const DELETE_THREAD_IMAGE = gql`
  mutation DeleteThreadImage($imageUrl: String!) {
    deleteThreadImage(imageUrl: $imageUrl)
  }
`;

export const REMOVE_USER_PROFILE_IMAGE = gql`
  mutation RemoveUserProfileImage {
    updateUserProfileImage(profileImageUrl: "") {
      id
      photoURL
      updatedAt
    }
  }
`;

export const GET_THREADS = gql`
  ${THREAD_FRAGMENT}
  query GetThreads($limit: Int, $offset: Int, $communityIds: [String!], $cursor: String, $since: String) {
    threads(limit: $limit, offset: $offset, communityIds: $communityIds, cursor: $cursor, since: $since) {
      ...ThreadFragment
    }
  }
`;

export const CREATE_THREAD = gql`
  ${THREAD_FRAGMENT}
  mutation CreateThread($input: CreateThreadInput!) {
    createThread(input: $input) {
      ...ThreadFragment
    }
  }
`;

export const GET_THREAD = gql`
  ${THREAD_FRAGMENT}
  query GetThread($id: ID!) {
    thread(id: $id) {
      ...ThreadFragment
    }
  }
`;

export const GET_COMMENTS = gql`
  query GetComments($threadId: ID!, $limit: Int, $offset: Int) {
    comments(threadId: $threadId, limit: $limit, offset: $offset) {
      __typename
      _id
      threadId
      author {
        __typename
        authorId
        displayName
        condition
        userType
        photoURL
      }
      content
      createdAt
      parentCommentId
      replyCount
      reactionCounts {
        love
        withYou
        funny
        insightful
        poop
      }
      myReaction
    }
  }
`;



export const CREATE_COMMENT = gql`
  mutation CreateComment($input: CreateCommentInput!) {
    createComment(input: $input) {
      __typename
      _id
      threadId
      communityId
      author {
        __typename
        authorId
        displayName
        condition
        userType
        photoURL
      }
      content
      createdAt
      parentCommentId
      replyCount
      reactionCounts {
        love
        withYou
        funny
        insightful
        poop
      }
      myReaction
    }
  }
`;

export const SAVE_PUSH_TOKEN = gql`
  mutation SavePushToken($input: SavePushTokenInput!) {
    savePushToken(input: $input)
  }
`;

export const REMOVE_PUSH_TOKEN = gql`
  mutation RemovePushToken($token: String!) {
    removePushToken(token: $token)
  }
`;

export const DELETE_THREAD = gql`
  ${THREAD_FRAGMENT}
  mutation DeleteThread($threadId: ID!) {
    deleteThread(threadId: $threadId) {
      ...ThreadFragment
    }
  }
`;

export const GET_THREAD_REACTIONS = gql`
  query GetThreadReactions($threadId: ID!) {
    threadReactions(threadId: $threadId) {
      id
      displayName
      condition
      userType
      photoURL
      reactionType
    }
  }
`;

export const GET_COMMENT_REACTIONS = gql`
  query GetCommentReactions($commentId: ID!) {
    commentReactions(commentId: $commentId) {
      id
      displayName
      condition
      userType
      photoURL
      reactionType
    }
  }
`;


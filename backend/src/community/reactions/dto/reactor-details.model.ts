import { ObjectType, Field, ID } from '@nestjs/graphql';

@ObjectType('ReactorDetails')
export class ReactorDetails {
  @Field(() => String)
  id: string;

  @Field(() => String)
  displayName: string;

  @Field(() => String, { nullable: true })
  condition?: string;

  @Field(() => String)
  userType: string;

  @Field(() => String, { nullable: true })
  photoURL?: string;

  @Field(() => String)
  reactionType: string;
}
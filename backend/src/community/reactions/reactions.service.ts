import { Injectable, NotFoundException, OnModuleInit, ServiceUnavailableException, Logger, forwardRef, Inject } from '@nestjs/common';
import { MongoService } from '../../mongo/mongo.service';
import { Collection, ObjectId } from 'mongodb';
import { Reaction } from './dto/reactions.models';
import { Thread } from '../threads/dto/threads.models';
import { Comment } from '../comments/dto/comments.models';
import { UsersService } from '../../users/users.service';
import { NotificationsService } from '../../notifications/notifications.service';
import { PrismaService } from '../../prisma/prisma.service';

@Injectable()
export class ReactionsService implements OnModuleInit {
  private threadsCollection: Collection<Thread> | null = null;
  private commentsCollection: Collection<Comment> | null = null;
  private reactionsCollection: Collection<Reaction> | null = null;
  private readonly logger = new Logger(ReactionsService.name);

  constructor(
    private readonly mongoService: MongoService,
    private readonly usersService: UsersService,
    private readonly notificationsService: NotificationsService,
    private readonly prismaService: PrismaService,
  ) {}

  private environment = process.env.NODE_ENV;
  private mongoDB = this.environment === 'production' ? 'chronicare-forum-prod' : 'chronicare-forum-dev';

  onModuleInit() {
    this.logger.log('Mongo DB: ', this.mongoDB);
    const db = this.mongoService.getDatabase(this.mongoDB);
    if (db) {
      this.threadsCollection = db.collection<Thread>('Threads');
      this.commentsCollection = db.collection<Comment>('Comments');
      this.reactionsCollection = db.collection<Reaction>('Reactions');
    } else {
      this.logger.warn('Database not available. ReactionsService will not be functional.'); 
    }
  }

  private checkDbConnection() {
    if (!this.threadsCollection || !this.commentsCollection || !this.reactionsCollection) {
      throw new ServiceUnavailableException('Database service is not available.');
    }
  }

  private async sendNotificationAsync(notificationPromise: Promise<void>): Promise<void> {
    setImmediate(async () => {
      try {
        await notificationPromise;
      } catch (error) {
        this.logger.error('Background notification failed:', error);
      }
    });
  }

  private shouldSkipNotification(reactorPseudonymId: string, authorPseudonymId: string): boolean {
    return reactorPseudonymId === authorPseudonymId;
  }

  async addReactionToThread(
    threadId: string,
    userId: string,
    reactionType: string,
  ): Promise<Thread> {
    this.checkDbConnection();
    const threadObjectId = new ObjectId(threadId);
    
    // Get or create pseudonym for the user
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    const pseudonymId = pseudonym.pseudonymId;
    
    const client = this.mongoService.getClient();
    if (!client) {
      throw new ServiceUnavailableException('Database client is not available.');
    }
    const session = client.startSession();

    let threadAuthorPseudonymId: string | null = null;
    let threadTitle: string | null = null;
    let shouldSendNotification = false;

    try {
      await session.withTransaction(async () => {
        // 1. Validate Thread exists
        const thread = await this.threadsCollection!.findOne({ _id: threadObjectId }, { session });
        if (!thread) {
          throw new NotFoundException(`Thread with ID ${threadId} not found.`);
        }
        
        // Store thread info for notification
        threadAuthorPseudonymId = thread.author.authorId;
        threadTitle = thread.title;

        // 2. Find any existing reaction by this user on this thread (using pseudonymId)
        const existingReaction = await this.reactionsCollection!.findOne({
          documentId: threadObjectId,
          reactorId: pseudonymId,
        }, { session });

        let isTogglingOff = false;

        // 3. If a reaction already exists, handle removal/update
        if (existingReaction) {
          const oldReactionType = existingReaction.reactionType;
          isTogglingOff = oldReactionType === reactionType;

          // Decrement the count of the old reaction
          await this.threadsCollection!.updateOne(
            { _id: threadObjectId },
            { $inc: { [`reactionCounts.${oldReactionType}`]: -1 } },
            { session }
          );
          
          // Remove the old reaction document
          await this.reactionsCollection!.deleteOne({ _id: existingReaction._id }, { session });
          this.logger.log(`User ${userId} removed reaction ${oldReactionType} from thread ${threadId}`);
        }
        
        // 4. If the user is not just toggling off, add the new reaction
        if (!isTogglingOff) {
          // Increment the count of the new reaction
          await this.threadsCollection!.updateOne(
            { _id: threadObjectId },
            { $inc: { [`reactionCounts.${reactionType}`]: 1 } },
            { session }
          );

          // Insert the new reaction document
          await this.reactionsCollection!.insertOne({
            documentId: threadObjectId,
            documentType: 'Thread',
            reactorId: pseudonymId,
            reactionType: reactionType,
            createdAt: new Date(),
            updatedAt: new Date(),
          } as Reaction, { session });
          this.logger.log(`User ${userId} added reaction ${reactionType} to thread ${threadId}`);
          if (!this.shouldSkipNotification(pseudonymId, threadAuthorPseudonymId!)) {
            shouldSendNotification = true;
          }
        }
      });
    } finally {
      await session.endSession();
    }

    // 5. Send notification if a new reaction was added (fire-and-forget)
    if (shouldSendNotification && threadAuthorPseudonymId && threadTitle) {
      this.sendNotificationAsync(
        this.notificationsService.sendThreadReactionNotification(
          threadId,
          userId,
          threadAuthorPseudonymId,
          threadTitle,
          reactionType,
        )
      );
    }

    // 6. Return the updated thread state
    return this.getThreadById(threadId);
  }

  async addReactionToComment(
    commentId: string,
    userId: string,
    reactionType: string,
  ): Promise<Comment> {
    this.checkDbConnection();
    const commentObjectId = new ObjectId(commentId);

    // Get or create pseudonym for the user
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    const pseudonymId = pseudonym.pseudonymId;

    const client = this.mongoService.getClient();
    if (!client) {
      throw new ServiceUnavailableException('Database client is not available.');
    }
    const session = client.startSession();

    let commentAuthorPseudonymId: string | null = null;
    let commentThreadId: string | null = null;
    let threadTitle: string | null = null;
    let shouldSendNotification = false;

    try {
      await session.withTransaction(async () => {
        // 1. Validate Comment exists
        const comment = await this.commentsCollection!.findOne({ _id: commentObjectId }, { session });
        if (!comment) {
          throw new NotFoundException(`Comment with ID ${commentId} not found.`);
        }
        
        // Store comment info for notification
        commentAuthorPseudonymId = comment.author.authorId;
        commentThreadId = comment.threadId.toString();
        
        // Get thread title for notification
        const thread = await this.threadsCollection!.findOne({ _id: comment.threadId }, { session });
        threadTitle = thread?.title || 'a post';

        // 2. Find any existing reaction by this user on this comment (using pseudonymId)
        const existingReaction = await this.reactionsCollection!.findOne({
          documentId: commentObjectId,
          reactorId: pseudonymId,
          documentType: 'Comment',
        }, { session });

        let isTogglingOff = false;

        // 3. If a reaction already exists, handle removal/update
        if (existingReaction) {
          const oldReactionType = existingReaction.reactionType;
          isTogglingOff = oldReactionType === reactionType;

          // Decrement the count of the old reaction
          await this.commentsCollection!.updateOne(
            { _id: commentObjectId },
            { $inc: { [`reactionCounts.${oldReactionType}`]: -1 } },
            { session }
          );

          // Remove the old reaction document
          await this.reactionsCollection!.deleteOne({ _id: existingReaction._id }, { session });
          this.logger.log(`User ${userId} removed reaction ${oldReactionType} from comment ${commentId}`);
        }

        // 4. If the user is not just toggling off, add the new reaction
        if (!isTogglingOff) {
          // Increment the count of the new reaction
          await this.commentsCollection!.updateOne(
            { _id: commentObjectId },
            { $inc: { [`reactionCounts.${reactionType}`]: 1 } },
            { session }
          );

          // Insert the new reaction document
          await this.reactionsCollection!.insertOne({
            documentId: commentObjectId,
            documentType: 'Comment',
            reactorId: pseudonymId,
            reactionType: reactionType,
            createdAt: new Date(),
            updatedAt: new Date(),
          } as Reaction, { session });
          this.logger.log(`User ${userId} added reaction ${reactionType} to comment ${commentId}`);
          if (!this.shouldSkipNotification(pseudonymId, commentAuthorPseudonymId!)) {
            shouldSendNotification = true;
          }
        }
      });
    } finally {
      await session.endSession();
    }

    // 5. Send notification if a new reaction was added (fire-and-forget)
    if (shouldSendNotification && commentAuthorPseudonymId && commentThreadId && threadTitle) {
      this.sendNotificationAsync(
        this.notificationsService.sendCommentReactionNotification(
          commentId,
          commentThreadId,
          userId,
          commentAuthorPseudonymId,
          threadTitle,
          reactionType,
        )
      );
    }

    // 6. Return the updated comment state
    return this.getCommentById(commentId);
  }

  async findUserReactionForThread(
    threadId: ObjectId,
    userId: string,
  ): Promise<Reaction | null> {
    this.checkDbConnection();
    
    // Convert userId to pseudonymId for querying
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    
    return this.reactionsCollection!.findOne({
      documentId: threadId,
      reactorId: pseudonym.pseudonymId,
      documentType: 'Thread',
    });
  }

  async findUserReactionsForThreads(
    threadIds: ObjectId[],
    userId: string,
  ): Promise<Reaction[]> {
    this.checkDbConnection();
    if (threadIds.length === 0) {
      return [];
    }
    
    // Convert userId to pseudonymId for querying
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    
    return this.reactionsCollection!
      .find({
        documentId: { $in: threadIds },
        reactorId: pseudonym.pseudonymId,
        documentType: 'Thread',
      })
      .toArray();
  }

  async findUserReactionForComment(
    commentId: ObjectId,
    userId: string,
  ): Promise<Reaction | null> {
    this.checkDbConnection();
    
    // Convert userId to pseudonymId for querying
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    
    return this.reactionsCollection!.findOne({
      documentId: commentId,
      reactorId: pseudonym.pseudonymId,
      documentType: 'Comment',
    });
  }

  async findUserReactionsForComments(
    commentIds: ObjectId[],
    userId: string,
  ): Promise<Reaction[]> {
    this.checkDbConnection();
    if (commentIds.length === 0) {
      return [];
    }
    
    // Convert userId to pseudonymId for querying
    const pseudonym = await this.usersService.getOrCreatePseudonym(userId);
    
    return this.reactionsCollection!
      .find({
        documentId: { $in: commentIds },
        reactorId: pseudonym.pseudonymId,
        documentType: 'Comment',
      })
      .toArray();
  }

  // Helper methods that need to access other collections
  private async getThreadById(id: string): Promise<Thread> {
    this.checkDbConnection();
    if (!ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    const thread = await this.threadsCollection!.findOne({ _id: new ObjectId(id) });
    if (!thread) {
      throw new NotFoundException(`Thread with ID ${id} not found.`);
    }
    return thread as Thread;
  }

  private async getCommentById(commentId: string): Promise<Comment> {
    this.checkDbConnection();
    if (!ObjectId.isValid(commentId)) {
      throw new NotFoundException('Invalid comment ID format.');
    }
    const comment = await this.commentsCollection!.findOne({ _id: new ObjectId(commentId) });
    if (!comment) {
      throw new NotFoundException(`Comment with ID ${commentId} not found.`);
    }
    return comment;
  }

  async getUserDetails(pseudonymIds: string[]): Promise<Map<string, any>> {
    if (pseudonymIds.length === 0) {
      return new Map();
    }
    const userDetails = await this.prismaService.client.$queryRaw`
      SELECT 
        up."pseudonymId",
        u."displayName",
        u."photoURL",
        udp."userRole"::text as userType,
        d."displayName" as condition,
        udp."isPrimary"
      FROM user_vault.user_pseudonyms up
      INNER JOIN identity.users u ON u.id = up."userId"
      LEFT JOIN health."UserDiseaseProfile" udp ON udp."user_healthID" = up."pseudonymId"
      LEFT JOIN public."Disease" d ON d.id = udp."diseaseId"
      WHERE up."pseudonymId" = ANY(${pseudonymIds})
    `;

      // Create a map for quick user detail lookups
      const userDetailsMap = new Map();
      (userDetails as any[]).forEach(user => {
        userDetailsMap.set(user.pseudonymId, {
          displayName: user.displayName,
          photoURL: user.photoURL,
          userType: user.usertype[0].toUpperCase() + user.usertype.slice(1).toLowerCase(),
          condition: user.condition
        });
      });

    return userDetailsMap;
  }

  async getThreadReactionsWithUserDetails(threadId: string): Promise<any[]> {
    this.checkDbConnection();
    const threadObjectId = new ObjectId(threadId);
    
    // Get all reactions for the thread
    const reactions = await this.reactionsCollection!
      .find({
        documentId: threadObjectId,
        documentType: 'Thread'
      })
      .toArray();

    if (reactions.length === 0) {
      return [];
    }

    // Extract pseudonym IDs
    const pseudonymIds = reactions.map(r => r.reactorId);
    const userDetailsMap = await this.getUserDetails(pseudonymIds); 


    // Combine reactions with user details
    return reactions.map(reaction => {
      const userDetail = userDetailsMap.get(reaction.reactorId) || {
        displayName: 'Unknown User',
        photoURL: null,
        userType: 'Unknown',
        condition: null
      };

      this.logger.log('Getting reactions for thread, userDetail: ', userDetail.displayName);

      return {
        id: reaction.reactorId,
        displayName: userDetail.displayName,
        condition: userDetail.condition,
        userType: userDetail.userType,
        photoURL: userDetail.photoURL,
        reactionType: reaction.reactionType
      };
    });
  }

  async getCommentReactionsWithUserDetails(commentId: string): Promise<any[]> {
    this.checkDbConnection();
    const commentObjectId = new ObjectId(commentId);
    
    // Get all reactions for the comment
    const reactions = await this.reactionsCollection!
      .find({
        documentId: commentObjectId,
        documentType: 'Comment'
      })
      .toArray();

    if (reactions.length === 0) {
      return [];
    }

    // Extract pseudonym IDs
    const pseudonymIds = reactions.map(r => r.reactorId);
    const userDetailsMap = await this.getUserDetails(pseudonymIds); 
  

    // Combine reactions with user details
    return reactions.map(reaction => {
      const userDetail = userDetailsMap.get(reaction.reactorId) || {
        displayName: 'Unknown User',
        photoURL: null,
        userType: 'Unknown',
        condition: null
      };
      this.logger.log('Getting reactions for thread, userDetail: ', userDetail.displayName);


      return {
        id: reaction.reactorId,
        displayName: userDetail.displayName,
        condition: userDetail.condition,
        userType: userDetail.userType,
        photoURL: userDetail.photoURL,
        reactionType: reaction.reactionType
      };
    });
  }
}

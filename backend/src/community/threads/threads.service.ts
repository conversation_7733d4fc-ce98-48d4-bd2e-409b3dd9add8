import { Injectable, NotFoundException, OnModuleInit, ServiceUnavailableException, Logger, ForbiddenException } from '@nestjs/common';
import { MongoService } from '../../mongo/mongo.service';
import { Collection, ObjectId } from 'mongodb';
import { Thread, CreateThreadInput } from './dto/threads.models';
import { NotificationsService } from '../../notifications/notifications.service';
import { UsersService } from '../../users/users.service';

@Injectable()
export class ThreadsService implements OnModuleInit {
  private threadsCollection: Collection<Thread> | null = null;
  private readonly logger = new Logger(ThreadsService.name);

  constructor(
    private readonly mongoService: MongoService,
    private readonly notificationsService: NotificationsService,
    private readonly usersService: UsersService,
  ) {}

  private environment = process.env.NODE_ENV;
  private mongoDB = this.environment === 'production' ? 'chronicare-forum-prod' : 'chronicare-forum-dev';

  onModuleInit() {
    this.logger.log('Mongo DB: ', this.mongoDB);
    const db = this.mongoService.getDatabase(this.mongoDB);
    if (db) {
      this.threadsCollection = db.collection<Thread>('Threads');
    } else {
      this.logger.warn('Database not available. ThreadsService will not be functional.'); 
    }
  }

  private checkDbConnection() {
    if (!this.threadsCollection) {
      throw new ServiceUnavailableException('Database service is not available.');
    }
  }

  async getThreads({ limit = 10, offset = 0, communityIds, cursor, since }: { limit?: number; offset?: number; communityIds?: string[]; cursor?: string, since?: string }): Promise<Thread[]> {
    this.checkDbConnection();
    this.logger.log('getThreads', { limit, offset, communityIds, cursor, since });
    
    // Hardcoded pinned thread IDs - filter out invalid ObjectIds
    const rawPinnedThreadIds = ['687792bf79786aadb1ebdd5e','6870e5f9ff43b5c63f2bb893'];
    const pinnedThreadIds = rawPinnedThreadIds.filter(id => ObjectId.isValid(id));
    
    // Build base query for community filtering and exclude soft-deleted threads
    const baseQuery: any = communityIds && communityIds.length > 0 ? { communityId: { $in: communityIds } } : {};
    baseQuery.deletedAt = { $exists: false };
    
    // Handle case where there are no pinned threads
    if (pinnedThreadIds.length === 0) {
      this.logger.log('No pinned threads to process, fetching regular threads only');
      return this.fetchRegularThreads(baseQuery, limit, offset, cursor, since);
    }
    
    // Fetch pinned threads separately (always fetch these regardless of pagination)
    const pinnedQuery = {
      ...baseQuery,
      _id: { $in: pinnedThreadIds.map(id => new ObjectId(id)) }
    };
    const pinnedThreads = await this.threadsCollection!
      .find(pinnedQuery)
      .toArray() as Thread[];
    
    // Sort pinned threads by their order in pinnedThreadIds array
    pinnedThreads.sort((a, b) => {
      const indexA = pinnedThreadIds.indexOf(a._id.toString());
      const indexB = pinnedThreadIds.indexOf(b._id.toString());
      return indexA - indexB;
    });
    
    // Calculate remaining limit for regular threads
    const remainingLimit = Math.max(0, limit - pinnedThreads.length);
    if (remainingLimit === 0) {
      return pinnedThreads;
    }
    
    // Build query for regular threads (exclude pinned threads)
    const regularQuery = {
      ...baseQuery,
      _id: { $nin: pinnedThreadIds.map(id => new ObjectId(id)) }
    };
    
    const regularThreads = await this.fetchRegularThreads(regularQuery, remainingLimit, offset, cursor, since);
    
    return [...pinnedThreads, ...regularThreads];
  }

  private async fetchRegularThreads(query: any, limit: number, offset: number, cursor?: string, since?: string): Promise<Thread[]> {
    this.logger.log(`Constructed query: ${JSON.stringify(query)}`);

    // Add 'since' filter if provided
    if (since) {
      query.createdAt = { $gt: new Date(since) };
      return await this.threadsCollection!
        .find(query)
        .sort({ createdAt: -1, _id: -1 })
        .limit(limit)
        .toArray() as Thread[];
    }
    
    // If cursor is provided, use cursor-based pagination instead of offset
    if (cursor) {
      if (!ObjectId.isValid(cursor)) {
        this.logger.warn(`Invalid cursor format: ${cursor}`);
        return [];
      }

      // For cursor-based pagination, get threads created before the cursor thread
      query._id = query._id ? { ...query._id, $lt: new ObjectId(cursor) } : { $lt: new ObjectId(cursor) };
      
      return await this.threadsCollection!
        .find(query)
        .sort({ createdAt: -1, _id: -1 })
        .limit(limit)
        .toArray() as Thread[];
    }
    
    // Fallback to offset-based pagination when no cursor is provided
    return await this.threadsCollection!
      .find(query)
      .sort({ createdAt: -1, _id: -1 })
      .skip(offset)
      .limit(limit)
      .toArray() as Thread[];
  }

  async getThreadById(id: string): Promise<Thread> {
    this.checkDbConnection();
    if (!ObjectId.isValid(id)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    const thread = await this.threadsCollection!.findOne({ _id: new ObjectId(id), deletedAt: { $exists: false } });
    if (!thread) {
      throw new NotFoundException(`Thread with ID ${id} not found.`);
    }
    return thread as Thread;
  }

  async createThread(createThreadInput: CreateThreadInput): Promise<Thread> {
    this.checkDbConnection();
    
    // Get or create pseudonym for the user
    const pseudonym = await this.usersService.getOrCreatePseudonym(createThreadInput.author.authorId);
    
    // Create author object with pseudonymId instead of userId
    const authorWithPseudonym = {
      ...createThreadInput.author,
      authorId: pseudonym.pseudonymId,
    };
    
    const newThread = {
      communityId: createThreadInput.communityId,
      author: authorWithPseudonym,
      title: createThreadInput.title,
      content: createThreadInput.content,
      labels: createThreadInput.labels || [],
      imageUrls: createThreadInput.imageUrls || [],
      status: 'active',
      createdAt: new Date(),
      updatedAt: new Date(),
      commentCount: 0,
      reactionCounts: {
        love: 0,
        withYou: 0,
        funny: 0,
        insightful: 0,
        poop: 0,
      },
    };
    const result = await this.threadsCollection!.insertOne(newThread as any);
    
    if (!result.insertedId) {
      throw new ServiceUnavailableException('Failed to create thread.');
    }

    this.logger.log(`Created new thread with ID: ${result.insertedId}`);
    
    const createdThread = {
      ...newThread,
      _id: result.insertedId,
    };

    this.notificationsService.sendNewThreadNotification(
      createdThread._id.toHexString(),
      createdThread.communityId,
      createThreadInput.author.authorId, // Use original userId for notifications
      createdThread.title,
      createdThread.content,
    );
    
    // Return the created thread object without an extra DB call.
    // We cast it to Thread as it's structurally compatible.
    return createdThread as Thread;
  }

  async incrementCommentCount(threadId: string): Promise<void> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    
    await this.threadsCollection!.updateOne(
      { _id: new ObjectId(threadId) },
      { $inc: { commentCount: 1 }, $set: { updatedAt: new Date() } }
    );
  }

  async decrementCommentCount(threadId: string): Promise<void> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }
    
    await this.threadsCollection!.updateOne(
      { _id: new ObjectId(threadId) },
      { $inc: { commentCount: -1 }, $set: { updatedAt: new Date() } }
    );
  }

  async softDeleteThread(threadId: string, userId: string): Promise<Thread> {
    this.checkDbConnection();
    if (!ObjectId.isValid(threadId)) {
      throw new NotFoundException('Invalid thread ID format.');
    }

    // First, get the thread to check if it exists and get author info
    const thread = await this.threadsCollection!.findOne({ 
      _id: new ObjectId(threadId),
      deletedAt: { $exists: false }
    });

    if (!thread) {
      throw new NotFoundException(`Thread with ID ${threadId} not found.`);
    }

    // Get the pseudonym for the requesting user
    const userPseudonym = await this.usersService.getOrCreatePseudonym(userId);

    // Check authorization - compare pseudonymIds
    if (thread.author.authorId !== userPseudonym.pseudonymId) {
      throw new ForbiddenException('You can only delete your own threads.');
    }

    // Perform soft delete
    const result = await this.threadsCollection!.updateOne(
      { _id: new ObjectId(threadId) },
      { 
        $set: { 
          deletedAt: new Date(),
          updatedAt: new Date()
        }
      }
    );

    if (result.modifiedCount === 0) {
      throw new ServiceUnavailableException('Failed to delete thread.');
    }

    // Return the updated thread
    const updatedThread = await this.threadsCollection!.findOne({ _id: new ObjectId(threadId) });
    return updatedThread as Thread;
  }
}

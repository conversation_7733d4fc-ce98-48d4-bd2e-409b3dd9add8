# ------------------------------------------------------
# THIS FILE WAS AUTOMATICALLY GENERATED (DO NOT MODIFY)
# ------------------------------------------------------

type Author {
  authorId: String!
  condition: String
  displayName: String!
  photoURL: String
  userType: String!
}

input AuthorInput {
  authorId: String!
  condition: String
  displayName: String!
  photoURL: String
  userType: String!
}

type Comment {
  _id: ID!
  author: Author!
  communityId: ID
  content: String!
  createdAt: DateTime!
  myReaction: String
  parentCommentId: ID
  reactionCounts: ReactionCounts!
  replyCount: Int!
  status: String!
  threadId: ID!
  updatedAt: DateTime!
}

type CommunityInfo {
  displayName: String
  id: ID!
  name: String!
}

input CompleteOnboardingInput {
  consent: ConsentInput!
  diseases: DiseaseDataInput!
  medicalDevices: MedicalDeviceDataInput
  medications: MedicationDataInput!
  personalInfo: PersonalInfoInput!
  profilePicture: ProfilePictureInput!
  userTypes: UserTypeDataInput!
}

input ConsentInput {
  dataPrivacy: Boolean!
  dataSharing: Boolean!
  marketing: Boolean!
}

input CreateCommentInput {
  author: AuthorInput!
  content: String!
  parentCommentId: ID
  threadId: ID!
}

input CreateFeedbackInput {
  categories: [String!]!
  name: String
  text: String!
}

input CreateThreadInput {
  author: AuthorInput!
  communityId: String!
  content: String!
  imageUrls: [String!]
  labels: [String!]
  title: String!
}

input CreateUserInput {
  displayName: String
  email: String!
  emailVerified: Boolean
  firebaseUid: String!
  onboardingCompleted: Boolean
  photoURL: String
}

"""
A date-time string at UTC, such as 2019-12-03T09:54:33Z, compliant with the date-time format.
"""
scalar DateTime

input DiseaseDataInput {
  selectedDiseases: [DiseaseSelectionInput!]!
}

input DiseaseSelectionInput {
  icdCode: String!
  id: String!
}

input MedicalDeviceDataInput {
  diseaseRelatedMedicalDevices: String!
}

input MedicationDataInput {
  diseaseRelatedMedications: String!
  unrelatedMedications: [MedicationEntryInput!]!
}

input MedicationEntryInput {
  dosage: String
  frequency: String
  isCurrent: Boolean!
  medication: MedicationItemInput!
  notes: String
  startDate: DateTime
}

input MedicationItemInput {
  id: String!
  label: String!
}

type Mutation {
  addCommentReaction(commentId: ID!, reaction: String!): Comment!
  addReaction(reaction: String!, threadId: ID!): Thread!
  completeOnboarding(data: CompleteOnboardingInput!): Boolean!
  createComment(input: CreateCommentInput!): Comment!
  createFeedback(createFeedbackInput: CreateFeedbackInput!): Boolean!
  createProfileImageUploadUrl(contentType: String!): UploadUrlResponse!
  createThread(input: CreateThreadInput!): Thread!
  createThreadImageUploadUrl(contentType: String!): UploadUrlResponse!
  createUser(input: CreateUserInput!): User!
  deleteThread(threadId: ID!): Thread!
  deleteThreadImage(imageUrl: String!): Boolean!
  deleteUser(id: ID!): User!
  removePushToken(token: String!): Boolean!
  savePushToken(input: SavePushTokenInput!): Boolean!
  updateProfilePicture(photoURL: String!): User!
  updateUserProfileImage(profileImageUrl: String!): User!
}

input PersonalInfoInput {
  birthdate: DateTime!
  countryCode: String!
  countryName: String!
  firstName: String!
  gender: String!
  language: String!
  lastName: String!
}

input ProfilePictureInput {
  imageUri: String
}

type Query {
  checkUserExistsByEmail(email: String!): Boolean!
  commentReactions(commentId: ID!): [ReactorDetails!]!
  comments(limit: Int = 20, offset: Int = 0, threadId: ID!): [Comment!]!
  getFirebaseUid: String!
  getMyPseudonym: UserPseudonym!
  getMyPseudonymId: String!
  me: User!
  replies(limit: Int = 10, offset: Int = 0, parentCommentId: ID!): [Comment!]!
  thread(id: ID!): Thread!
  threadReactions(threadId: ID!): [ReactorDetails!]!
  threads(communityIds: [String!], cursor: String, limit: Int = 10, offset: Int = 0, since: String): [Thread!]!
  user(id: ID!): User
  users: [User!]!
}

type ReactionCounts {
  funny: Int!
  insightful: Int!
  love: Int!
  poop: Int!
  withYou: Int!
}

type ReactorDetails {
  condition: String
  displayName: String!
  id: String!
  photoURL: String
  reactionType: String!
  userType: String!
}

input SavePushTokenInput {
  token: String!
}

type Thread {
  _id: ID!
  author: Author!
  commentCount: Int!
  communityId: String!
  content: String!
  createdAt: DateTime!
  deletedAt: DateTime
  imageUrls: [String!]!
  isAuthor: Boolean!
  labels: [String!]!
  myReaction: String
  reactionCounts: ReactionCounts!
  status: String!
  title: String!
  updatedAt: DateTime!
}

type UploadUrlResponse {
  publicUrl: String!
  signedUrl: String!
}

type User {
  communities: [CommunityInfo!]
  condition: String
  consent: UserConsent
  createdAt: DateTime!
  creationTime: DateTime
  displayName: String
  email: String!
  emailVerified: Boolean!
  firebaseUid: String!
  firstName: String
  id: ID!
  lastName: String
  lastSignInTime: DateTime
  onboardingCompleted: Boolean!
  phoneNumber: String
  photoURL: String
  updatedAt: DateTime!
  userType: String
}

type UserConsent {
  consentDataPrivacy: Boolean!
  consentDataPrivacyAt: DateTime
  consentMarketing: Boolean!
  consentMarketingAt: DateTime
  consentTermsOfService: Boolean!
  consentTermsOfServiceAt: DateTime
  id: ID!
}

type UserPseudonym {
  createdAt: DateTime!
  id: ID!
  pseudonymId: String!
  updatedAt: DateTime!
  userId: String!
}

input UserTypeDataInput {
  diseaseUserTypes: String!
}
import { Injectable, On<PERSON><PERSON>uleI<PERSON>t, OnM<PERSON>ule<PERSON><PERSON>roy, Logger } from '@nestjs/common';
import { PrismaClient } from '@prisma/client';
import { withAccelerate } from '@prisma/extension-accelerate';

// This type will be used for the extended client
export type ExtendedPrismaClient = ReturnType<
  typeof createExtendedPrismaClient
>;

// This function creates the extended client instance
function createExtendedPrismaClient() {
  return new PrismaClient().$extends(withAccelerate());
}

@Injectable()
// We change from `extends PrismaClient` to just implementing the lifecycle hooks.
// This is because the `extends` pattern is too rigid for use with Prisma's client extensions.
export class PrismaService implements OnModuleInit, OnModuleDestroy {
  private readonly logger = new Logger(PrismaService.name);
  // The client is now of the exported type
  readonly client: ExtendedPrismaClient = createExtendedPrismaClient();

  constructor() {
    // The client is initialized via the exported function
  }

  async onModuleInit() {
    try {
      // With Accelerate, explicit connection management is handled for you,
      // but it's safe to keep these hooks.
      await this.client.$connect();
      this.logger.log('Prisma service initialized and connected via Accelerate.');
    } catch (error) {
      this.logger.error('Failed to initialize Prisma service:', error);
      throw error;
    }
  }

  async onModuleDestroy() {
    try {
      await this.client.$disconnect();
    } catch (error) {
      this.logger.error('Failed to disconnect Prisma client:', error);
      throw error;
    }
  }
}
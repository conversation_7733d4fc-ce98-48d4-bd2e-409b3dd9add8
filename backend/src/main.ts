import { NestFactory, Reflector } from '@nestjs/core';
import { <PERSON><PERSON><PERSON><PERSON><PERSON><PERSON>, Logger } from '@nestjs/common';
import { AppModule } from './app.module';
import { JwtAuthGuard } from './auth/jwt-auth.guard';

async function bootstrap() {
  const logger = new Logger('Bootstrap');

  try {
    const app = await NestFactory.create(AppModule, {
      logger: process.env.NODE_ENV === 'production' 
        ? ['debug', 'warn', 'error','log','verbose']
        : ['log', 'warn', 'error', 'verbose', 'debug'],
    });
    console.log('NODE_ENV', process.env.NODE_ENV);
    
    // Configure CORS for production
    const corsOptions = process.env.NODE_ENV === 'production' 
      ? {
          // For mobile apps, we can be more permissive with origins
          origin: (origin, callback) => {
            // Allow requests with no origin (mobile apps)
            if (!origin) return callback(null, true);
            
            // Allow specific origins for development and any future web versions
            const allowedOrigins = [
              'http://localhost:8081', // Expo dev server
              'exp://localhost:8081', // Expo development
              // Add web domains here if you create a web version later
            ];
            
            if (allowedOrigins.includes(origin)) {
              return callback(null, true);
            }
            
            // For mobile apps in production, you might want to allow all origins
            // since mobile apps don't have the same origin concept as browsers
            return callback(null, true);
          },
          credentials: true,
          methods: ['GET', 'POST', 'PUT', 'DELETE', 'OPTIONS'],
          allowedHeaders: ['Content-Type', 'Authorization', 'Apollo-Require-Preflight'],
          optionsSuccessStatus: 200,
        }
      : true; // Allow all origins in development

    app.enableCors(corsOptions);
    
    // Enable validation pipes
    app.useGlobalPipes(new ValidationPipe({ 
      whitelist: true,
      forbidNonWhitelisted: true,
      transform: true,
    }));

    // Apply global authentication guard
    const reflector = app.get(Reflector);
    app.useGlobalGuards(new JwtAuthGuard(reflector));
    
    // Use PORT environment variable (required for Cloud Run)
    const port = process.env.PORT || 3000;
    
    // Bind to all interfaces in production (required for containers)
    const host = '0.0.0.0';
    
    await app.listen(port, host);
    
    const isProduction = process.env.NODE_ENV === 'production';
    logger.debug(`Application is running on: ${isProduction ? `http://${host}:${port}` : `http://localhost:${port}`}`);
    logger.log(`GraphQL endpoint: ${isProduction ? `http://${host}:${port}/graphql` : `http://localhost:${port}/graphql`}`);
    
    if (isProduction) {
      logger.debug('🚀 Production mode: Server is ready to receive requests');
    } else {
      logger.debug('🔧 Development mode: GraphQL Playground available');
    }

  } catch (error) {
    logger.error('Failed to start application:', error);
    process.exit(1);
  }
}

bootstrap();
